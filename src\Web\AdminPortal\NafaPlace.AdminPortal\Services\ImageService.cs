using System;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.JSInterop;

namespace NafaPlace.AdminPortal.Services
{
    public class ImageService
    {
        private readonly HttpClient _httpClient;
        private readonly IJSRuntime _jsRuntime;
        private readonly string _apiBaseUrl = "http://localhost:5000"; // URL de la Gateway API
        private int _lastUploadedImageId = 0;

        public ImageService(HttpClient httpClient, IJSRuntime jsRuntime)
        {
            _httpClient = httpClient;
            _httpClient.BaseAddress = new Uri(_apiBaseUrl);
            _jsRuntime = jsRuntime;
        }

        public async Task<string> ConvertFileToBase64(IBrowserFile file)
        {
            try
            {
                // Limiter la taille du fichier à 5 Mo
                var maxFileSize = 5 * 1024 * 1024;
                
                // Utiliser MemoryStream pour éviter les lectures incorrectes
                using var ms = new MemoryStream();
                await using var stream = file.OpenReadStream(maxFileSize);
                
                // Copier le contenu du stream dans le MemoryStream
                await stream.CopyToAsync(ms);
                
                // Convertir en base64
                return Convert.ToBase64String(ms.ToArray());
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }

        // Nouvelle méthode qui fonctionne exactement comme le portail vendeur
        public async Task<string> UploadProductImageAsync(IBrowserFile file, int productId, bool isMain = false)
        {
            try
            {
                // Vérification de la taille et du type de fichier (comme le portail vendeur)
                if (file.Size > 5 * 1024 * 1024) // 5 MB max
                {
                    throw new Exception("La taille du fichier ne doit pas dépasser 5 MB.");
                }

                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif" };
                var extension = Path.GetExtension(file.Name).ToLowerInvariant();
                if (!Array.Exists(allowedExtensions, e => e == extension))
                {
                    throw new Exception("Le format du fichier n'est pas pris en charge. Utilisez JPG, PNG ou GIF.");
                }

                // Lire le fichier en mémoire (exactement comme le portail vendeur)
                using var stream = file.OpenReadStream(5 * 1024 * 1024); // 5 MB max
                using var ms = new MemoryStream();
                await stream.CopyToAsync(ms);
                ms.Position = 0;

                // Convertir l'image en base64 (exactement comme le portail vendeur)
                var imageBytes = ms.ToArray();
                var base64Image = Convert.ToBase64String(imageBytes);

                // Créer un objet JSON pour envoyer l'image à l'API (exactement comme le portail vendeur)
                var requestObject = new
                {
                    Image = base64Image,
                    IsMain = isMain
                };

                var jsonContent = new StringContent(
                    JsonSerializer.Serialize(requestObject),
                    Encoding.UTF8,
                    "application/json");

                // Envoyer la requête à l'API (exactement comme le portail vendeur)
                var response = await _httpClient.PostAsync($"/api/v1/products/{productId}/images", jsonContent);

                if (response.IsSuccessStatusCode)
                {
                    var jsonString = await response.Content.ReadAsStringAsync();

                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };

                    // L'API simple retourne une seule image
                    var result = JsonSerializer.Deserialize<ProductImageUploadResult>(jsonString, options);

                    // Stocker l'ID de l'image pour une utilisation ultérieure
                    _lastUploadedImageId = result?.Id ?? 0;

                    Console.WriteLine($"Image téléchargée avec succès. URL: {result?.Url}, ImageData: {(result?.ImageData != null ? "présent" : "absent")}");

                    // Si l'API ne renvoie pas ImageData, utilisons l'image base64 que nous avons envoyée
                    if (string.IsNullOrEmpty(result?.ImageData) && !string.IsNullOrEmpty(base64Image))
                    {
                        // Préfixer avec le type MIME pour en faire une URL data valide
                        string mimeType = GetMimeTypeFromExtension(extension);
                        if (result != null)
                        {
                            result.ImageData = $"data:{mimeType};base64,{base64Image}";
                        }
                        Console.WriteLine("ImageData ajouté localement car absent de la réponse API");
                    }

                    return result?.Url ?? string.Empty;
                }

                var error = await response.Content.ReadAsStringAsync();
                throw new Exception($"Erreur lors de l'envoi de l'image à l'API: {error}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors du téléchargement de l'image : {ex.Message}");
                throw;
            }
        }

        // Méthode de compatibilité pour l'ancienne signature (pour éviter les erreurs de compilation)
        public async Task<string> UploadProductImageAsync(int productId, string base64Image, bool isMain = false)
        {
            try
            {
                // Vérifier si l'image en base64 est valide
                if (string.IsNullOrEmpty(base64Image))
                {
                    return string.Empty;
                }

                // Supprimer le préfixe data:image si présent
                string cleanBase64 = base64Image;
                if (base64Image.Contains(","))
                {
                    cleanBase64 = base64Image.Split(',')[1];
                }

                // Créer un objet JSON simple comme dans le portail vendeur
                var requestObject = new
                {
                    Image = cleanBase64,
                    IsMain = isMain
                };

                var content = new StringContent(
                    JsonSerializer.Serialize(requestObject),
                    Encoding.UTF8,
                    "application/json");

                // Utiliser la même route que le portail vendeur
                var response = await _httpClient.PostAsync($"/api/v1/products/{productId}/images", content);

                if (response.IsSuccessStatusCode)
                {
                    var jsonString = await response.Content.ReadAsStringAsync();

                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };

                    var result = JsonSerializer.Deserialize<ProductImageUploadResult>(jsonString, options);

                    // Stocker l'ID de l'image pour une utilisation ultérieure
                    _lastUploadedImageId = result?.Id ?? 0;

                    return result?.Url ?? string.Empty;
                }

                var error = await response.Content.ReadAsStringAsync();
                throw new Exception($"Erreur lors de l'envoi de l'image à l'API: {error}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors du téléchargement de l'image : {ex.Message}");
                throw;
            }
        }

        // Méthode helper pour obtenir le type MIME (comme dans le portail vendeur)
        private string GetMimeTypeFromExtension(string extension)
        {
            return extension.ToLowerInvariant() switch
            {
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".gif" => "image/gif",
                _ => "image/jpeg"
            };
        }

        public async Task<bool> DeleteProductImageAsync(int productId, int imageId)
        {
            try
            {
                // Utiliser la même route que le portail vendeur
                var response = await _httpClient.DeleteAsync($"/api/v1/products/{productId}/images/{imageId}");
                
                return response.IsSuccessStatusCode;
            }
            catch (Exception)
            {
                return false;
            }
        }
        
        public async Task<bool> SetMainImageAsync(int productId, int imageId)
        {
            try
            {
                // Utiliser la même route que le portail vendeur
                var response = await _httpClient.PutAsync($"/api/v1/products/{productId}/images/{imageId}/main", null);
                
                return response.IsSuccessStatusCode;
            }
            catch (Exception)
            {
                return false;
            }
        }
        
        public Task<int> GetLastUploadedImageId()
        {
            return Task.FromResult(_lastUploadedImageId);
        }
        
        // Méthode générique pour uploader une image
        public async Task<ImageUploadResult> UploadImageAsync(IBrowserFile file, string category = "general")
        {
            try
            {
                // Convertir le fichier en base64
                var base64Image = await ConvertFileToBase64(file);

                if (string.IsNullOrEmpty(base64Image))
                {
                    return new ImageUploadResult { Success = false, ErrorMessage = "Impossible de convertir le fichier en base64" };
                }

                // Utiliser la méthode appropriée selon la catégorie
                string imageUrl = category.ToLower() switch
                {
                    "categories" => await UploadCategoryImageAsync(base64Image),
                    _ => await UploadCategoryImageAsync(base64Image) // Par défaut, utiliser la méthode de catégorie
                };

                if (!string.IsNullOrEmpty(imageUrl))
                {
                    return new ImageUploadResult { Success = true, ImageUrl = imageUrl };
                }
                else
                {
                    return new ImageUploadResult { Success = false, ErrorMessage = "Échec de l'upload de l'image" };
                }
            }
            catch (Exception ex)
            {
                return new ImageUploadResult { Success = false, ErrorMessage = ex.Message };
            }
        }

        // Méthode pour uploader une image de catégorie
        public async Task<string> UploadCategoryImageAsync(string base64Image)
        {
            try
            {
                // Vérifier si l'image en base64 est valide
                if (string.IsNullOrEmpty(base64Image))
                {
                    return string.Empty;
                }

                // Supprimer le préfixe data:image si présent
                string cleanBase64 = base64Image;
                if (base64Image.Contains(","))
                {
                    cleanBase64 = base64Image.Split(',')[1];
                }

                // Créer le contenu JSON pour l'API
                var imageRequest = new
                {
                    Image = cleanBase64
                };

                var content = new StringContent(
                    JsonSerializer.Serialize(imageRequest),
                    Encoding.UTF8,
                    "application/json");

                // Envoyer la requête à l'API Gateway
                var response = await _httpClient.PostAsync("/api/v1/categories/upload-image", content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<CategoryImageUploadResult>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    return result?.ImageUrl ?? string.Empty;
                }

                return string.Empty;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de l'upload de l'image de catégorie: {ex.Message}");
                return string.Empty;
            }
        }

        // Classe pour désérialiser la réponse de l'API (identique au portail vendeur)
        private class ProductImageUploadResult
        {
            public int Id { get; set; }
            public string Url { get; set; } = string.Empty;
            public bool IsMain { get; set; }
            public int ProductId { get; set; }
            public string? ImageData { get; set; }

            // Propriété pour compatibilité
            public string ImageUrl => Url;
        }

        // Classe pour désérialiser la réponse de l'upload d'image de catégorie
        private class CategoryImageUploadResult
        {
            public string ImageUrl { get; set; } = string.Empty;
        }
    }

    // Classe publique pour les résultats d'upload d'images
    public class ImageUploadResult
    {
        public bool Success { get; set; }
        public string ImageUrl { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
    }
}
