using Microsoft.EntityFrameworkCore;
using NafaPlace.Inventory.Application.DTOs;
using NafaPlace.Inventory.Application.Interfaces;
using NafaPlace.Inventory.Domain.Enums;
using NafaPlace.Inventory.Domain.Models;
using NafaPlace.Inventory.Infrastructure.Data;
using System.Net.Http.Json;
using System.Text.Json;

namespace NafaPlace.Inventory.Infrastructure.Repositories;

public class ProductStockRepository : IProductStockRepository
{
    private readonly InventoryDbContext _context;
    private readonly HttpClient _httpClient;

    public ProductStockRepository(InventoryDbContext context, HttpClient httpClient)
    {
        _context = context;
        _httpClient = httpClient;
    }

    public async Task<int> GetCurrentStockAsync(int productId)
    {
        try
        {
            // Appeler l'API Catalog pour obtenir le stock actuel
            var response = await _httpClient.GetAsync($"api/catalog/products/{productId}/stock");
            if (response.IsSuccessStatusCode)
            {
                var stockText = await response.Content.ReadAsStringAsync();
                if (int.TryParse(stockText, out var stock))
                {
                    return stock;
                }
            }
        }
        catch (Exception)
        {
            // En cas d'erreur, retourner 0
        }

        return 0;
    }

    public async Task<bool> UpdateStockAsync(int productId, int newQuantity)
    {
        try
        {
            // Appeler l'API Catalog pour mettre à jour le stock
            var updateData = new { Quantity = newQuantity };
            var response = await _httpClient.PutAsJsonAsync($"api/v1/products/{productId}/stock", updateData);
            return response.IsSuccessStatusCode;
        }
        catch (Exception)
        {
            return false;
        }
    }



    public async Task<int> GetTotalProductsCountAsync(int? sellerId = null)
    {
        try
        {
            var url = sellerId.HasValue 
                ? $"api/products/count?sellerId={sellerId}" 
                : "api/products/count";
            
            var response = await _httpClient.GetAsync(url);
            if (response.IsSuccessStatusCode)
            {
                var countText = await response.Content.ReadAsStringAsync();
                if (int.TryParse(countText, out var count))
                {
                    return count;
                }
            }
        }
        catch (Exception)
        {
            // En cas d'erreur
        }

        return 0;
    }

    public async Task<int> GetLowStockCountAsync(int? sellerId = null, int threshold = 10)
    {
        try
        {
            var url = sellerId.HasValue
                ? $"api/v1/products/low-stock-count?threshold={threshold}&sellerId={sellerId}"
                : $"api/v1/products/low-stock-count?threshold={threshold}";

            var response = await _httpClient.GetAsync(url);
            if (response.IsSuccessStatusCode)
            {
                var countText = await response.Content.ReadAsStringAsync();
                if (int.TryParse(countText, out var count))
                {
                    return count;
                }
            }
        }
        catch (Exception)
        {
            // En cas d'erreur
        }

        return 0;
    }

    public async Task<int> GetOutOfStockCountAsync(int? sellerId = null)
    {
        try
        {
            var url = sellerId.HasValue 
                ? $"api/products/out-of-stock-count?sellerId={sellerId}" 
                : "api/products/out-of-stock-count";
            
            var response = await _httpClient.GetAsync(url);
            if (response.IsSuccessStatusCode)
            {
                var countText = await response.Content.ReadAsStringAsync();
                if (int.TryParse(countText, out var count))
                {
                    return count;
                }
            }
        }
        catch (Exception)
        {
            // En cas d'erreur
        }

        return 0;
    }

    public async Task<decimal> GetTotalInventoryValueAsync(int? sellerId = null)
    {
        try
        {
            var url = sellerId.HasValue 
                ? $"api/products/inventory-value?sellerId={sellerId}" 
                : "api/products/inventory-value";
            
            var response = await _httpClient.GetAsync(url);
            if (response.IsSuccessStatusCode)
            {
                var valueText = await response.Content.ReadAsStringAsync();
                if (decimal.TryParse(valueText, out var value))
                {
                    return value;
                }
            }
        }
        catch (Exception)
        {
            // En cas d'erreur
        }

        return 0;
    }

    public async Task<bool> RecalculateStockLevelsAsync()
    {
        try
        {
            var response = await _httpClient.PostAsync("api/products/recalculate-stock", null);
            return response.IsSuccessStatusCode;
        }
        catch (Exception)
        {
            return false;
        }
    }

    // Méthodes manquantes de l'interface
    public async Task<bool> UpdateStockAsync(int productId, int newQuantity, string reason, string updatedBy, string? notes = null)
    {
        try
        {
            // Enregistrer le mouvement
            await RecordMovementAsync(productId, MovementType.Adjustment, newQuantity, reason, updatedBy, notes);

            // Mettre à jour le stock via l'API Catalog
            var updateData = new { Quantity = newQuantity };
            var response = await _httpClient.PutAsJsonAsync($"api/v1/products/{productId}/stock", updateData);
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }

    public async Task<bool> AdjustStockAsync(int productId, int newQuantity, string reason, string updatedBy, string? notes = null)
    {
        return await UpdateStockAsync(productId, newQuantity, reason, updatedBy, notes);
    }

    public async Task<StockValidationResult> ValidateStockAvailabilityAsync(int productId, int quantity)
    {
        var availableStock = await GetAvailableStockAsync(productId);
        return new StockValidationResult
        {
            IsValid = availableStock >= quantity,
            AvailableStock = availableStock,
            RequestedQuantity = quantity,
            ErrorMessage = availableStock >= quantity ? null : "Stock insuffisant"
        };
    }

    public async Task<int> GetAvailableStockAsync(int productId)
    {
        return await GetCurrentStockAsync(productId);
    }

    public async Task<bool> RecordMovementAsync(int productId, MovementType movementType, int quantity, string reason, string performedBy, string? notes = null)
    {
        try
        {
            // Récupérer les informations du produit
            var productInfo = await GetProductInfoAsync(productId);
            var currentStock = await GetCurrentStockAsync(productId);
            var previousStock = movementType == MovementType.Sale || movementType == MovementType.Damage
                ? currentStock + quantity
                : currentStock - quantity;

            var movement = new StockMovement
            {
                ProductId = productId,
                ProductName = productInfo?.Name ?? $"Product {productId}",
                Type = movementType,
                Quantity = quantity,
                PreviousStock = previousStock,
                NewStock = currentStock,
                Reason = reason,
                UserId = performedBy,
                SellerId = productInfo?.SellerId ?? 0,
                CreatedAt = DateTime.UtcNow,
                Notes = notes
            };

            _context.StockMovements.Add(movement);
            await _context.SaveChangesAsync();
            return true;
        }
        catch
        {
            return false;
        }
    }

    private async Task<ProductInfo?> GetProductInfoAsync(int productId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/catalog/products/{productId}/info");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                var productInfo = System.Text.Json.JsonSerializer.Deserialize<ProductInfo>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                return productInfo;
            }
        }
        catch (Exception)
        {
            // En cas d'erreur, retourner null
        }

        return null;
    }


}
